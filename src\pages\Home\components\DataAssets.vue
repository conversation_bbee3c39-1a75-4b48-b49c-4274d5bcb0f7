<template>
  <ul class="data-assets">
    <li v-for="(item, i) in dataList" :key="i">
      <img :src="item.img" :alt="item.name" />
      <div class="content">
        <p>{{ item.num }}</p>
        <p>{{ item.name }}</p>
      </div>
    </li>
  </ul>
</template>
<script setup lang="ts">
import database from '../img/database.webp'
import files from '../img/files.webp'
import table from '../img/table.webp'
import source from '../img/source.webp'
import { ref, watch } from 'vue'

const props = defineProps<{ dataTotal: any }>()

const dataList = ref<any[]>([])

watch(
  () => props.dataTotal,
  (newVal) => {
    if (newVal) {
      dataList.value = [
        {
          img: source,
          num: newVal.datasourceCount || 0,
          name: '数据源/个',
        },
        {
          img: database,
          num: newVal.databaseCount || 0,
          name: '数据库/个',
        },
        {
          img: table,
          num: newVal.tableCount || 0,
          name: '数据表/张',
        },
        {
          img: files,
          num: newVal.fileCount || 0,
          name: '文件数/个',
        },
      ]
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>
<style lang="less" scoped>
.data-assets {
  padding-top: 0.7813vw;
  flex: 1;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 0 0.7813vw;
  align-items: center;
  li {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    overflow: hidden;
    img {
      width: 3.3333vw;
    }
    .content {
      margin-left: 0.7813vw;
      flex: 1;
      p:nth-child(1) {
        font-size: 1.25vw;
        color: var(--el-color-primary);
        line-height: 1.25vw;
        font-weight: 700;
      }
      p:nth-child(2) {
        margin-top: 0.7813vw;
        font-size: 0.625vw;
        color: #666666;
      }
    }
  }
}
</style>
