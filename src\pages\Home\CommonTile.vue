<template>
  <div class="title">
    <div class="left">
      <img :src="icon" alt="icon" />
      <p>{{ props.name }}</p>
      
    </div>
    <ul class="right" v-if="props.isActiveShow">
      <li
        v-for="p in tabList"
        @click="handleClick(p.key)"
        :class="{ active: [active].includes(p.key) }"
        :key="p.key"
      >
        {{ p.name }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import icon from '@/assets/homeImg/icon.webp'
import { ref } from 'vue'

const active = ref<number>(1)
const tabList: any[] = [
  {
    name: '数量',
    key: 1,
  },
  {
    name: '存储',
    key: 0,
  },
]

const handleClick = (key: number) => {
  active.value = key
  emit('handleChange', active.value)
}

const emit = defineEmits<{
  (e: 'handleChange', key: number): void
}>()
const props = withDefaults(
  defineProps<{
    name: string
    isActiveShow?: boolean
  }>(),
  {
    isActiveShow: true,
  },
)
</script>

<style scoped lang="less">
.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    display: flex;
    align-items: center;
    img {
      width: 0.4688vw;
      height: 0.4688vw;
    }
    p {
      margin-left: 0.5208vw;
      font-size: 0.8333vw;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #333333;
    }
  }
  .right {
    display: flex;
    align-items: center;
    li {
      font-size: 0.625vw;
      font-weight: 400;
      color: #656565;
      cursor: pointer;
    }
    li + li {
      margin-left: 0.5208vw;
    }
    li.active {
      color: var(--el-color-primary);
      position: relative;
      &::after {
        content: '';
        width: 1.5625vw;
        height: 0.1042vw;
        background-color: var(--el-color-primary);
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
}
</style>
