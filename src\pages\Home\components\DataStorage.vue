<template>
  <ul class="data-storage">
    <li class="item">
      <img :src="storage" alt="storage" />
      <div class="total">
        <p>
          <span>{{ dataTotal.allSize }}</span
          ><span>{{ dataTotal.allSizeUnit }}</span>
        </p>
        <p>数据存储总量-接入率</p>
      </div>
      <div class="progress">
        <img src="../img/line.webp" alt="line" />
        <div class="progress-total">
          <div class="progress-item">
            <p>
              <span>数据湖</span><span>{{ totalStoragePercentage.file }}%</span>
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item"
                :style="{ width: getProgressWidth(totalStoragePercentage.file) }"
              ></div>
            </div>
          </div>
          <div class="progress-item">
            <p>
              <span>数据仓</span><span>{{ totalStoragePercentage.table }}%</span>
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item blue"
                :style="{ width: getProgressWidth(totalStoragePercentage.table) }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </li>
    <li class="item">
      <img :src="todayStorage" alt="todayStorage" />
      <div class="total">
        <p>
          <span>{{ dataTotal.intervalAllSize }}</span
          ><span>{{ dataTotal.intervalAllSizeUnit }}</span>
        </p>
        <p>今日新增数据量</p>
      </div>
      <div class="progress">
        <img src="../img/line.webp" alt="line" />
        <div class="progress-total">
          <div class="progress-item">
            <p>
              <span>数据湖</span><span>{{ intervalStoragePercentage.file }}%</span>
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item"
                :style="{ width: getProgressWidth(intervalStoragePercentage.file) }"
              ></div>
            </div>
          </div>
          <div class="progress-item">
            <p>
              <span>数据仓</span><span>{{ intervalStoragePercentage.table }}%</span>
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item blue"
                :style="{ width: getProgressWidth(intervalStoragePercentage.table) }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ul>
</template>
<script lang="ts" setup>
import storage from '../img/storage.webp'
import todayStorage from '../img/todayStorage.webp'
import { computed } from 'vue'

const props = defineProps<{ dataTotal: any }>()

// 将不同单位的数据转换为统一单位（MB）
const convertToMB = (size: number, unit: string): number => {
  const sizeNum = Number(size) || 0
  switch (unit?.toUpperCase()) {
    case 'GB':
      return sizeNum * 1024
    case 'TB':
      return sizeNum * 1024 * 1024
    case 'KB':
      return sizeNum / 1024
    case 'MB':
    default:
      return sizeNum
  }
}

// 计算总存储量的百分比
const totalStoragePercentage = computed(() => {
  const fileSizeMB = convertToMB(props.dataTotal?.allFileSize, props.dataTotal?.allFileSizeUnit)
  const tableSizeMB = convertToMB(props.dataTotal?.allTableSize, props.dataTotal?.allTableSizeUnit)
  const total = fileSizeMB + tableSizeMB

  if (total === 0) return { file: 0, table: 0 }

  const filePercentage = (fileSizeMB / total) * 100
  const tablePercentage = (tableSizeMB / total) * 100

  return {
    file: Math.round(filePercentage * 10) / 10,
    table: Math.round(tablePercentage * 10) / 10,
  }
})

// 计算今日新增的百分比
const intervalStoragePercentage = computed(() => {
  const fileSizeMB = convertToMB(
    props.dataTotal?.intervalFileSize,
    props.dataTotal?.intervalFileSizeUnit,
  )
  const tableSizeMB = convertToMB(
    props.dataTotal?.intervalTableSize,
    props.dataTotal?.intervalTableSizeUnit,
  )
  const total = fileSizeMB + tableSizeMB

  if (total === 0) return { file: 0, table: 0 }

  const filePercentage = (fileSizeMB / total) * 100
  const tablePercentage = (tableSizeMB / total) * 100

  return {
    file: Math.round(filePercentage * 10) / 10,
    table: Math.round(tablePercentage * 10) / 10,
  }
})

// 计算进度条宽度（设置最小宽度为10%以保证美观）
const getProgressWidth = (percentage: number): string => {
  const minWidth = 10 // 最小宽度10%
  const width = Math.max(percentage, minWidth)
  return `${Math.min(width, 100)}%`
}
</script>
<style scoped lang="less">
.data-storage {
  margin: 0 0.5208vw;
  display: flex;
  box-sizing: border-box;
  .item {
    height: 7.8125vw;
    flex: 1;
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    padding: 0.7813vw 1.5625vw;
    overflow: hidden;
    display: flex;
    align-items: center;
    > img {
      width: 4.1667vw;
    }
    > .total {
      margin-left: 1.0417vw;
      text-align: left;
      > p:nth-child(1) {
        color: var(--el-color-primary);
        > :nth-child(1) {
          font-size: 1.875vw;
          font-weight: 700;
        }
        > :nth-child(2) {
          margin-left: 0.2604vw;
          font-size: 0.7292vw;
          font-weight: 700;
        }
      }
      > p:nth-child(2) {
        // margin-top: 0.2604vw;
        font-size: 0.7292vw;
        color: #333333;
      }
    }
    > .progress {
      flex: 1;
      margin-left: 3.125vw;
      display: flex;
      box-sizing: border-box;
      > img {
        height: 5.5208vw;
        padding-top: 0.4167vw;
        box-sizing: border-box;
      }
      .progress-total {
        margin-left: 0.4167vw;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
        .progress-item {
          > p:nth-child(1) {
            display: flex;
            align-items: center;
            > :nth-child(1) {
              font-size: 0.7292vw;
              color: #333333;
            }
            > :nth-child(2) {
              margin-left: 0.7292vw;
              color: #91d7ff;
              font-size: 0.9375vw;
            }
          }
          .progress-bar {
            display: flex;
            margin-top: 0.2604vw;
            height: 4px;
            background: rgba(76, 76, 76, 0.53);
            border-radius: 2px;
            .progress-bar-item {
              height: 100%;
              background: #91d7ff;
              border-radius: 2px;
              position: relative;
              transition: width 0.3s ease;
              &::before {
                content: '';
                width: 6px;
                height: 6px;
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                background-color: @withe;
                border-radius: 50%;
                border: 1px solid #91d7ff;
              }
            }
            .blue {
              background: var(--el-color-primary);
              &::before {
                border-color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }
  }
  li + li {
    margin-left: 0.5208vw;
  }
}
</style>
