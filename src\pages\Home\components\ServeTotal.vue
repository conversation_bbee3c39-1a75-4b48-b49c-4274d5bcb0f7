<template>
  <div class="RightTop">
    <div class="left">
      <img :src="server" alt="server" />
      <span>{{ totalData }}</span>
      <span>服务发布数/个</span>
    </div>
    <div class="right">
      <Echarts v-if="list.length" :options="option" />
    </div>
  </div>
</template>

<script setup lang="ts">
import server from '@/assets/homeImg/server.svg'
import { EChartsOption } from 'echarts'
import Echarts from '@/components/Echarts/index.vue'
import { serverPublishApi } from '@/api/home'
import { ElMessage } from 'element-plus'
import { computed, ref } from 'vue'

const list = ref<any[]>([])
const totalData = ref<number>(0)
const initData = async () => {
  try {
    const { data, status, message, total } = await serverPublishApi()
    if ([200].includes(status)) {
      list.value = data
      totalData.value = total
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
initData()

const getMaxNumber = () => {
  const arr: number[] = list.value.map((item: any) => Number(item.data))
  const max = Math.max(...arr) > 0 ? Math.max(...arr) : 1
  arr.fill(max)
  return arr
}

const option = computed<EChartsOption>(() => ({
  title: {
    show: false,
  },
  tooltip: {
    trigger: 'item',
    show: false,
  },
  grid: {
    borderWidth: 0,
    top: '10%',
    left: '0',
    right: '0',
    bottom: '0',
  },
  yAxis: [
    {
      type: 'category',
      inverse: true,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: false,
        inside: false,
      },
      data: list.value.map((item: any) => item.desc),
    },
    {
      type: 'category',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        inside: true,
        lineHeight: 20,
        align: 'right',
        verticalAlign: 'bottom',
        formatter: function (val: any) {
          return `${val}`
        },
      },
      splitArea: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      data: list.value.map((item: any) => item.data).reverse(),
    },
  ],
  xAxis: {
    type: 'value',
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    splitLine: {
      show: false,
    },
    axisLabel: {
      show: false,
    },
  },
  series: [
    {
      name: 'total',
      type: 'bar',
      zlevel: 1,
      barGap: '-100%',
      barWidth: '4px',
      data: getMaxNumber(),
      legendHoverLink: false,
      itemStyle: {
        color: '#E8EDF4',
        borderRadius: 10,
      },
      emphasis: {
        itemStyle: {
          color: '#E8EDF4',
        },
      },
    },
    {
      name: 'bar',
      type: 'bar',
      zlevel: 2,
      barWidth: '4px',
      data: list.value.map((item: any) => item.data),
      itemStyle: {
        color: '#295BFD',
        borderRadius: 10,
      },
      label: {
        show: true,
        position: [0, '-16px'],
        formatter: function (a: any) {
          const str = ` {color|${a.name}}`
          return str
        },
        fontSize: 12,
        rich: {
          color: {
            color: '#656565',
          },
        },
      },
    },
  ],
}))
</script>

<style scoped lang="less">
.RightTop {
  flex: 1;
  box-sizing: border-box;
  padding: 1.5625vw 0;
  display: flex;
  .left {
    width: 40%;
    height: 100%;
    background: linear-gradient(to top right, #436cf9, #537aff, #87a3ff, #c2dafd);
    border-radius: 4px 4px 4px 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: @withe;
    img {
      width: 2.0833vw;
      height: 2.0833vw;
    }
    :nth-child(2) {
      font-size: 1.6667vw;
      margin: 0.2604vw 0;
    }
    :nth-child(3) {
      font-size: 0.625vw;
    }
  }
  .right {
    margin-left: 1.0417vw;
    flex: 1;
    box-sizing: border-box;
  }
}
</style>
